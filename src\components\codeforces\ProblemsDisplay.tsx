// ============================================================================
// PROBLEMS DISPLAY COMPONENT - AUTHENTICATION INTEGRATION & UX FIXES
// ============================================================================
//
// 🎯 COMPONENT PURPOSE:
// This component displays Codeforces problems and handles user authentication
// for creating coding sheets. It serves as the primary user interface for
// authentication flow and includes comprehensive error handling for auth issues.
//
// 🚨 AUTHENTICATION UX FIXES IMPLEMENTED:
//
// 1. FALSE NEGATIVE ERROR HANDLING (Critical UX Fix):
//    - Problem: Users saw "Login Failed" popup even when authentication succeeded
//    - Root Cause: signInWithPopup returned error despite successful session creation
//    - Technical Cause: PKCE errors occurred in background even when user was logged in
//    - Solution: Check actual session state after supposed "authentication failure"
//    - Result: Users see success message when they're actually logged in
//
// 2. DUAL AUTHENTICATION STATE VERIFICATION:
//    - Strategy: Never trust auth function result alone
//    - Implementation: Always verify session existence independently
//    - Timing: Wait 1 second after "failure" then check session
//    - Fallback: If session exists, treat as success regardless of error
//
// 3. PKCE ERROR CATEGORIZATION (Better Error Messages):
//    - Problem: Technical PKCE errors confused users
//    - Solution: Detect PKCE-specific errors and provide better messaging
//    - User-friendly: "Authentication encountered a technical error" vs raw PKCE message
//    - Actionable: Suggests refresh page for technical errors
//
// 🔄 AUTHENTICATION FLOW FROM USER PERSPECTIVE:
//
// Step 1: USER INTERACTION
//    - User clicks "Login with Google" button
//    - Button shows loading state with spinner
//    - Authentication modal appears
//    ↓
// Step 2: POPUP AUTHENTICATION
//    - Popup window opens with Google OAuth
//    - User completes Google authentication
//    - Popup handles code exchange and closes
//    ↓
// Step 3: RESULT PROCESSING (THIS COMPONENT)
//    - Receives result from signInWithPopup()
//    - If success: Show success message and update UI
//    - If "failure": Wait and check actual session state
//    ↓
// Step 4: SESSION VERIFICATION (FAIL-SAFE)
//    - Call createClient().auth.getSession() directly
//    - If session exists: Override "failure", treat as success
//    - If no session: Display appropriate error message
//    ↓
// Step 5: UI UPDATE
//    - Invalidate React Query caches
//    - Update authentication-dependent components
//    - Show success/error alert modal
//
// 🛡️ ERROR RESILIENCE STRATEGIES:
//
// 1. OPTIMISTIC SUCCESS DETECTION:
//    ```typescript
//    // Don't trust function result alone
//    if (!result.success) {
//      await new Promise(resolve => setTimeout(resolve, 1000));
//      const { data: { session } } = await createClient().auth.getSession();
//      if (session?.user) {
//        // User is actually logged in - treat as success!
//        handleAuthenticationSuccess();
//        return;
//      }
//    }
//    ```
//
// 2. EXCEPTION RECOVERY:
//    ```typescript
//    try {
//      const result = await signInWithPopup();
//      // Handle result...
//    } catch (error) {
//      // Even if exception thrown, check if user got logged in
//      const session = await checkSession();
//      if (session) {
//        handleAuthenticationSuccess(); // Override exception
//      }
//    }
//    ```
//
// 3. ERROR MESSAGE INTELLIGENCE:
//    ```typescript
//    const isPKCEError = error.includes("auth code and code verifier");
//    if (isPKCEError) {
//      showTechnicalErrorMessage(); // User-friendly
//    } else {
//      showActualAuthError(); // Real authentication issue
//    }
//    ```
//
// 🎨 USER EXPERIENCE IMPROVEMENTS:
//
// 1. LOADING STATES:
//    - Button shows spinner during authentication
//    - Prevents multiple simultaneous auth attempts
//    - Clear visual feedback for user actions
//
// 2. INTELLIGENT ERROR MESSAGES:
//    - PKCE errors: "Technical error, please try again"
//    - Real auth errors: "Authentication failed, please try again"
//    - Network errors: Specific network-related guidance
//
// 3. AUTOMATIC UI UPDATES:
//    - Query invalidation after successful auth
//    - Component re-renders with authenticated state
//    - Sheet creation buttons become available
//
// 4. SUCCESS CONFIRMATION:
//    - Clear success message after authentication
//    - Explains what user can now do (create sheets)
//    - Positive reinforcement for successful login
//
// 🔍 DEBUGGING AUTHENTICATION ISSUES:
//
// 1. Console logs to watch for:
//    - "Authentication succeeded despite error: [error message]"
//    - "Popup login error: [exception details]"
//    - "Session check error: [session verification issues]"
//
// 2. Authentication state indicators:
//    - Button text changes: "Login with Google" → "Signing in..." → (authenticated state)
//    - Sheet creation section appears/disappears based on auth
//    - User email appears in UI after successful auth
//
// 3. Common debugging scenarios:
//    - Auth succeeds but user sees error: Check session verification logs
//    - Popup doesn't open: Check popup blocker settings
//    - Multiple auth attempts: Check if isAuthenticating state works correctly
//
// 🏗️ INTEGRATION WITH AUTHENTICATION SYSTEM:
//
// 1. AUTH POPUP MODULE (/lib/auth-popup.ts):
//    - This component calls signInWithPopup()
//    - Handles popup creation and BroadcastChannel communication
//    - Returns success/failure result
//
// 2. POPUP CALLBACK (/auth/popup-callback):
//    - Handles OAuth code exchange in popup context
//    - Sends results back to this component via BroadcastChannel
//    - Ensures PKCE verifier availability
//
// 3. AUTH CONTEXT (/lib/auth-context.tsx):
//    - Provides global authentication state
//    - Updates automatically when session changes
//    - Used by this component to show/hide auth-dependent UI
//
// 4. REACT QUERY INTEGRATION:
//    - userInfo, sheetInfo, sheetDetails queries
//    - Automatically invalidated after successful authentication
//    - Provides loading states and error handling
//
// 📚 RELATED AUTHENTICATION FILES:
// - /lib/auth-popup.ts: Main authentication handler
// - /auth/popup-callback/page.tsx: OAuth callback processor
// - /lib/auth-context.tsx: Global authentication state
// - /components/ui/reusable-modal.tsx: Codeforces verification modal
//
// ⚠️ CRITICAL AUTHENTICATION PRINCIPLES:
//
// 1. NEVER TRUST AUTH FUNCTION RESULTS ALONE
//    - Always verify session state independently
//    - Handle both success and failure scenarios
//    - Prioritize actual user session over function return values
//
// 2. HANDLE RACE CONDITIONS GRACEFULLY
//    - Use delays to allow for session synchronization
//    - Check session multiple times if needed
//    - Provide fallback success detection
//
// 3. OPTIMIZE FOR USER EXPERIENCE
//    - Show success when user is actually logged in
//    - Provide clear, actionable error messages
//    - Never leave users confused about auth state
//
// 4. MAINTAIN AUTHENTICATION CONSISTENCY
//    - Keep UI state synchronized with actual auth state
//    - Invalidate caches after auth changes
//    - Update all auth-dependent components together
//
// ============================================================================

"use client";

import { CodingSheetInfo } from "@/components/sheets/CodingSheetLimitWarning";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { TwoPartModal } from "@/components/ui/reusable-modal";
import { useAuth } from "@/lib/auth-context";
import { signInWithPopup } from "@/lib/auth-popup";

import type { CodeforcesSubmission } from "@/lib/codeforces";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import axios from "axios";
import {
  AlertTriangle,
  Calendar,
  CheckCircle,
  FileText,
  Info,
  Trash2,
  XCircle,
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React from "react";

// Import query key utilities for consistent cache management

// ============================================================================
// PROBLEMS DISPLAY COMPONENT - IMPLEMENTATION OVERVIEW
// ============================================================================
//
// This component handles the complete workflow from displaying Codeforces problems
// to creating and managing coding sheets. Here's what has been implemented:
//
// 🎯 CURRENT IMPLEMENTATION:
//
// 1. PROBLEM DISPLAY ENGINE:
//    - Displays filtered Codeforces submissions with deduplication
//    - Shows: problem name, rating (color-coded), tags, contest info, solve date
//    - Handles pagination (100 initial + 50 more per load) for performance
//    - Generates proper Codeforces URLs for each problem
//    - Rating color system matches official Codeforces colors
//
// 2. SHEET CREATION SYSTEM:
//    - Converts problems to SheetProblem format for database storage
//    - Enforces 400 problem limit with validation
//    - Requires user authentication and Codeforces handle verification
//    - Shows rating distribution analysis before creation
//    - Handles sheet naming with input validation
//
// 3. CUSTOM ALERT MODAL SYSTEM (Recently Added):
//    - Replaced all browser alert() calls with Shadcn Dialog modals
//    - Types: success, error, warning, info, confirm
//    - Color-coded with appropriate icons (CheckCircle, XCircle, etc.)
//    - Confirmation modals have Cancel/Confirm buttons for destructive actions
//    - Success modals can trigger callbacks (like navigation)
//
// 4. SHEET MANAGEMENT:
//    - Users have sheet limits (currently 1 sheet max per user)
//    - Create sheet modal with name input and rating distribution preview
//    - Delete sheet modal with individual sheet selection
//    - Real-time sheet count tracking and limit enforcement
//    - Sheet limit warnings and promotion callouts
//
// 5. USER AUTHENTICATION FLOW:
//    - Integration with auth context for user state
//    - Codeforces handle verification modal (TwoPartModal)
//    - Different UI states: not logged in, logged in without handle, verified
//    - Handle verification triggers sheet creation access
//
// 6. API INTEGRATION (React Query):
//    - /api/userInfo - Get user's Codeforces handle
//    - /api/sheetInfo - Get user's sheet count
//    - /api/sheetInfo/customSheet - CRUD operations for sheets
//    - Automatic query invalidation on data changes
//    - Error handling with retry logic and user-friendly messages
//
// 7. STATE MANAGEMENT:
//    - Local state: pagination, modal visibility, form inputs, loading states
//    - Memoized computations: problem deduplication, rating distribution
//    - React Query: server state caching and synchronization
//    - Alert modal state with type-specific styling and actions
//
// 8. UI/UX FEATURES:
//    - Dark theme with gradient backgrounds and blur effects
//    - Loading skeletons and spinner states
//    - Hover effects and smooth transitions
//    - Responsive design with proper mobile handling
//    - Professional callout cards for sheet creation promotion
//
// 🔧 KEY FUNCTIONS:
//    - handleCreateSheet() - Opens creation modal with validation
//    - handleConfirmCreateSheet() - Processes sheet creation with API call
//    - handleDeleteSheet() - Shows confirmation then deletes sheet
//    - showAlert() - Unified alert modal system
//    - Problem deduplication and sorting logic
// ============================================================================

interface ProblemsDisplayProps {
  submissions: CodeforcesSubmission[]; // Filtered submissions to display
  isLoading?: boolean; // Whether data is still loading
  error?: string | null; // Error message if any
  dateRange?: {
    startDate: string;
    endDate: string;
  } | null; // Date range for context
}

// Interface for simplified problem structure used in coding sheets
interface SheetProblem {
  problemUrl: string;
  problemRating: string;
  tags: string[];
  problemName: string;
}

// Alert modal types
type AlertType = "success" | "error" | "warning" | "info" | "confirm";

interface AlertModalState {
  isOpen: boolean;
  type: AlertType;
  title: string;
  message: string;
  onConfirm?: () => void;
  onCancel?: () => void;
  confirmText?: string;
  cancelText?: string;
}

// Helper function to get rating color based on Codeforces rating system
const getRatingColor = (rating: number | undefined): string => {
  if (!rating) return "#9ca3af"; // Gray for unrated
  if (rating >= 3000) return "#8B0000"; // Legendary Grandmaster (Maroon)
  if (rating >= 2600) return "#ff0000"; // International Grandmaster (red)
  if (rating >= 2400) return "#ff0000"; // Grandmaster (red)
  if (rating >= 2300) return "#ff8c00"; // International Master (orange)
  if (rating >= 2100) return "#ff8c00"; // Master (orange)
  if (rating >= 1900) return "#aa00aa"; // Candidate Master (purple)
  if (rating >= 1600) return "#0000ff"; // Expert (blue)
  if (rating >= 1400) return "#00aaaa"; // Specialist (cyan)
  if (rating >= 1200) return "#008000"; // Pupil (green)
  return "#808080"; // Newbie (gray)
};

// Helper function to format submission date
const formatDate = (timestamp: number): string => {
  return new Date(timestamp * 1000).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

// Helper function to generate Codeforces problem URL
const getProblemUrl = (submission: CodeforcesSubmission): string => {
  const { problem } = submission;
  if (problem.contestId) {
    return `https://codeforces.com/contest/${problem.contestId}/problem/${problem.index}`;
  } else if (problem.problemsetName) {
    return `https://codeforces.com/problemset/problem/${problem.problemsetName}/${problem.index}`;
  }
  return "https://codeforces.com/problemset";
};

export const ProblemsDisplay: React.FC<ProblemsDisplayProps> = ({
  submissions,
  isLoading = false,
  error = null,
  dateRange = null,
}) => {
  // Router hook for navigation to custom sheet page
  const router = useRouter();

  // Get user from auth context
  const { user } = useAuth();

  const { data: userInfo, isLoading: isUserInfoLoading } = useQuery({
    queryKey: ["userInfo", user?.email],
    queryFn: async () => {
      if (!user?.email) {
        throw new Error("User email not found");
      }
      const response = await axios.get(`/api/userInfo`);
      return response.data;
    },
    enabled: !!user?.email,
    staleTime: 2 * 60 * 1000, // Cache for 2 minutes (reduced from 15 minutes) - userInfo changes less frequently
    gcTime: 10 * 60 * 1000, // Keep in cache for 10 minutes (reduced from 30 minutes)
    refetchOnWindowFocus: false, // Refetch on window focus to get latest sheet counts
    refetchOnMount: true, // Refetch on mount to ensure fresh data
    retry: (failureCount, error: any) => {
      // Don't retry if the error is a 404 Not Found
      if (error.response?.status === 404) {
        return false;
      }
      // For other errors, retry once
      return failureCount < 1;
    },
  });

  // Query client for invalidating queries
  const queryClient = useQueryClient();

  // Fetch sheet count using React Query and Axios
  const { data: sheetData, isLoading: sheetLoading } = useQuery({
    queryKey: ["sheetInfo"], // FIXED: Use consistent pattern without user email
    queryFn: async () => {
      if (!user?.email) {
        throw new Error("User email not found");
      }
      const response = await axios.get(`/api/sheetInfo`);
      return response.data;
    },
    enabled: !!user?.email, // Only run query if user email exists
    staleTime: 30 * 1000, // Consider data fresh for only 30 seconds
    gcTime: 5 * 60 * 1000, // Keep in cache for 5 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: true, // Always refetch when component mounts for error recovery
    retry: (failureCount, error: any) => {
      // Retry auth errors once
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        return failureCount < 1;
      }
      return failureCount < 1;
    },
  });

  // Fetch sheet details (including limit info) when user is logged in
  const { data: sheetDetails, isLoading: sheetDetailsLoading } = useQuery({
    queryKey: ["sheetDetails"],
    queryFn: async () => {
      if (!user?.email) {
        throw new Error("User email not found");
      }
      try {
        const response = await axios.get(`/api/sheetInfo/customSheet`);
        return response.data;
      } catch (error) {
        // If no sheets found or other error, return default values
        console.log("Sheet details fetch error:", error);
        return {
          sheets: [],
          count: 0,
          maxSheetSlots: 1, // Default limit
          isAllowedToCreateMore: true,
        };
      }
    },
    enabled: !!user?.email, // Run whenever user is logged in to get limit info
    staleTime: 30 * 1000, // Consider data fresh for only 30 seconds (reduced from 15 minutes)
    gcTime: 5 * 60 * 1000, // Keep in cache for 5 minutes (reduced from 30 minutes)
    refetchOnWindowFocus: true, // Refetch when user switches back to tab
    refetchOnMount: true, // Always refetch when component mounts
    retry: 1,
  });

  // Pagination state to handle large numbers of problems (like maspy with 10k+ problems)
  // Start with 100 problems and load 50 more each time "Show More" is clicked
  const [displayCount, setDisplayCount] = React.useState(100);

  const [isVerifyModalOpen, setIsVerifyModalOpen] = React.useState(false);
  // Modal state for create sheet confirmation
  const [isCreateSheetModalOpen, setIsCreateSheetModalOpen] =
    React.useState(false);

  // Sheet name input state
  const [sheetName, setSheetName] = React.useState("");

  // Loading state for sheet creation
  const [isCreatingSheet, setIsCreatingSheet] = React.useState(false);

  // Loading state for sheet deletion - track which specific sheet is being deleted
  const [deletingSheetId, setDeletingSheetId] = React.useState<string | null>(
    null
  );

  // Modal state for delete sheet selection
  const [isDeleteModalOpen, setIsDeleteModalOpen] = React.useState(false);

  // Loading state for popup authentication
  const [isAuthenticating, setIsAuthenticating] = React.useState(false);

  // Alert modal state
  const [alertModal, setAlertModal] = React.useState<AlertModalState>({
    isOpen: false,
    type: "info",
    title: "",
    message: "",
  });

  // Helper function to show alert modal
  const showAlert = (
    type: AlertType,
    title: string,
    message: string,
    onConfirm?: () => void,
    onCancel?: () => void,
    confirmText?: string,
    cancelText?: string
  ) => {
    setAlertModal({
      isOpen: true,
      type,
      title,
      message,
      onConfirm,
      onCancel,
      confirmText: confirmText || (type === "confirm" ? "Confirm" : "OK"),
      cancelText: cancelText || "Cancel",
    });
  };

  // Helper function to close alert modal
  const closeAlert = () => {
    setAlertModal((prev) => ({ ...prev, isOpen: false }));
  };

  // Function to handle popup-based login
  const handleTabLogin = async () => {
    if (isAuthenticating) return;

    setIsAuthenticating(true);

    try {
      const result = await signInWithPopup("google", {
        width: 500,
        height: 600,
        timeout: 300000,
      });

      if (result.success) {
        // FIXED: Use centralized invalidation for consistency
        // Invalidate all user-related queries after successful login
        queryClient.invalidateQueries({ queryKey: ["userInfo"] });
        queryClient.invalidateQueries({ queryKey: ["userProfile"] });
        queryClient.invalidateQueries({ queryKey: ["sheetInfo"] });
        queryClient.invalidateQueries({ queryKey: ["sheetDetails"] });
        queryClient.invalidateQueries({ queryKey: ["customSheets"] });
        queryClient.invalidateQueries({ queryKey: ["verificationStatus"] });
        queryClient.invalidateQueries({ queryKey: ["isVerified"] });

        showAlert(
          "success",
          "Login Successful!",
          "You have successfully logged in. You can now create coding sheets."
        );
      } else {
        showAlert(
          "error",
          "Login Failed",
          result.error || "Authentication failed. Please try again."
        );
      }
    } catch (error) {
      console.error("Login error:", error);
      showAlert(
        "error",
        "Login Failed",
        "An unexpected error occurred during login. Please try again."
      );
    } finally {
      setIsAuthenticating(false);
    }
  };

  // Function to handle opening the create sheet modal
  const handleCreateSheet = () => {
    // Check if user has reached their sheet limit
    if (sheetDetails && !sheetDetails.isAllowedToCreateMore) {
      showAlert(
        "warning",
        "Sheet Limit Reached",
        `You have reached your maximum sheet limit of ${sheetDetails.maxSheetSlots} sheets. You currently have ${sheetDetails.count} sheets.`
      );
      return;
    }

    setSheetName(""); // Reset sheet name when opening modal
    setIsCreateSheetModalOpen(true);
  };

  // Function to handle confirming sheet creation
  const handleConfirmCreateSheet = async () => {
    // Validate sheet name
    if (!sheetName.trim()) {
      showAlert("warning", "Missing Sheet Name", "Please enter a sheet name.");
      return;
    }

    // Validate user is logged in
    if (!user?.email) {
      showAlert("warning", "Login Required", "Please login to create a sheet.");
      return;
    }

    // Check if problems exceed the limit - don't allow creation
    if (sheetProblemsArrayToSaveIntoTheDatabase.length > 400) {
      showAlert(
        "warning",
        "Too Many Problems",
        `Cannot create sheet with ${sheetProblemsArrayToSaveIntoTheDatabase.length} problems. Please reduce the number of problems to 400 or fewer using filters (tags, rating range, date range, etc.) before creating a sheet.`
      );
      return;
    }

    setIsCreatingSheet(true);

    try {
      const response = await axios.post("/api/sheetInfo", {
        sheetName: sheetName.trim(),
        email: user.email,
        problems: sheetProblemsArrayToSaveIntoTheDatabase,
      });

      if (response.status === 200) {
        // FIXED: Success - invalidate sheet-related queries consistently
        queryClient.invalidateQueries({ queryKey: ["sheetInfo"] });
        queryClient.invalidateQueries({ queryKey: ["sheetDetails"] });
        queryClient.invalidateQueries({ queryKey: ["customSheets"] });

        // Close modal and navigate to the custom sheet page
        setIsCreateSheetModalOpen(false);
        setSheetName("");

        showAlert(
          "success",
          "Sheet Created Successfully!",
          "Your coding sheet has been created successfully. You will be redirected to view your sheets.",
          () => router.push("/customsheet")
        );
      } else {
        throw new Error("Failed to create sheet");
      }
    } catch (error: any) {
      console.error("Error creating coding sheet:", error);

      if (error.response?.status === 400) {
        if (error.response?.data === "Sheet limit reached") {
          showAlert(
            "error",
            "Sheet Limit Reached",
            "You have reached the maximum number of sheets (1). Please delete an existing sheet to create a new one."
          );
        } else {
          showAlert(
            "error",
            "Invalid Request",
            "Invalid request. Please check your input and try again."
          );
        }
      } else if (error.response?.status === 404) {
        showAlert(
          "error",
          "User Not Found",
          "User not found. Please make sure you are logged in."
        );
      } else {
        showAlert(
          "error",
          "Creation Failed",
          "An error occurred while creating the coding sheet. Please try again."
        );
      }
    } finally {
      setIsCreatingSheet(false);
    }
  };

  const handleVerificationComplete = (_handle: string) => {
    // FIXED: Invalidate all userInfo queries consistently
    queryClient.invalidateQueries({ queryKey: ["userInfo"] });
  };

  // Function to handle sheet deletion
  const handleDeleteSheet = async (sheetId?: string) => {
    // Determine which sheet to delete
    let targetSheetId = sheetId;

    if (!targetSheetId) {
      // If no specific sheet ID provided, use the first available
      if (sheetDetails?.sheets && sheetDetails.sheets.length > 0) {
        targetSheetId = sheetDetails.sheets[0].id;
      }
    }

    if (!targetSheetId) {
      showAlert(
        "error",
        "Sheet Not Found",
        "Sheet ID not found. Please refresh the page and try again."
      );
      return;
    }

    const sheetToDelete = sheetDetails?.sheets?.find(
      (sheet: any) => sheet.id === targetSheetId
    );
    const sheetName = sheetToDelete?.name || "this sheet";

    // Show confirmation modal
    showAlert(
      "confirm",
      "Delete Sheet",
      `Are you sure you want to delete "${sheetName}"? This action cannot be undone.`,
      async () => {
        setDeletingSheetId(targetSheetId);

        try {
          const response = await axios.delete("/api/sheetInfo/customSheet", {
            data: { id: targetSheetId },
          });

          if (response.status === 200) {
            // Success - invalidate queries to refresh the data
            queryClient.invalidateQueries({ queryKey: ["sheetInfo"] });
            queryClient.invalidateQueries({ queryKey: ["sheetDetails"] });
            queryClient.invalidateQueries({ queryKey: ["customSheets"] });

            showAlert(
              "success",
              "Sheet Deleted",
              `Sheet "${sheetName}" deleted successfully!`
            );

            // Close the modal after successful deletion
            setIsDeleteModalOpen(false);
          } else {
            throw new Error("Failed to delete sheet");
          }
        } catch (error: any) {
          console.error("Error deleting coding sheet:", error);

          if (error.response?.status === 401) {
            showAlert(
              "error",
              "Unauthorized",
              "You are not authorized to delete this sheet. Please log in and try again."
            );
          } else if (error.response?.status === 400) {
            showAlert(
              "error",
              "Invalid Request",
              "Invalid request. Please refresh the page and try again."
            );
          } else {
            showAlert(
              "error",
              "Deletion Failed",
              "An error occurred while deleting the coding sheet. Please try again."
            );
          }
        } finally {
          setDeletingSheetId(null);
        }
      }
    );
  };

  // Deduplicate submissions and sort by creation time (newest first)
  // This ensures no duplicate problems appear even if they slip through from the API
  const sortedSubmissions = React.useMemo(() => {
    // Create a Map to store unique problems using the same key logic as the API
    const uniqueProblems = new Map<string, CodeforcesSubmission>();

    for (const submission of submissions) {
      const problemKey = submission.problem.contestId
        ? `${submission.problem.contestId}${submission.problem.index}`
        : `${submission.problem.problemsetName || "unknown"}${
            submission.problem.index
          }`;

      // Keep the latest submission for each problem (highest creation time)
      if (
        !uniqueProblems.has(problemKey) ||
        submission.creationTimeSeconds >
          uniqueProblems.get(problemKey)!.creationTimeSeconds
      ) {
        uniqueProblems.set(problemKey, submission);
      }
    }

    // Convert to array and sort by creation time (newest first)
    return Array.from(uniqueProblems.values()).sort(
      (a, b) => b.creationTimeSeconds - a.creationTimeSeconds
    );
  }, [submissions]);

  // Memoized array of problems in the requested format for coding sheets - won't re-render unnecessarily
  const sheetProblemsArrayToSaveIntoTheDatabase =
    React.useMemo((): SheetProblem[] => {
      if (!sortedSubmissions || sortedSubmissions.length === 0) {
        return [];
      }

      return sortedSubmissions
        .filter((submission: CodeforcesSubmission) => {
          // Only include problems that have a rating (remove unrated problems)
          return submission.problem.rating && submission.problem.rating > 0;
        })
        .sort((a: CodeforcesSubmission, b: CodeforcesSubmission) => {
          // Sort by rating in ascending order (lowest to highest)
          return a.problem.rating! - b.problem.rating!;
        })
        .map((submission: CodeforcesSubmission) => {
          const { problem } = submission;

          // Generate problem URL
          const problemUrl = problem.contestId
            ? `https://codeforces.com/contest/${problem.contestId}/problem/${problem.index}`
            : `https://codeforces.com/problemset/problem/${
                problem.problemsetName || "gym"
              }/${problem.index}`;

          // Format rating as string (we know it exists due to filter)
          const problemRating = problem.rating!.toString();

          // Get tags array
          const tags = problem.tags || [];

          return {
            problemUrl,
            problemRating,
            tags,
            problemName: problem.name,
          };
        })
        .slice(0, 400); // Return only the first 400 problems after all processing
    }, [sortedSubmissions]); // Only re-compute when sortedSubmissions changes

  // Calculate rating distribution for the modal
  const ratingDistribution = React.useMemo(() => {
    const distribution: Record<string, number> = {};

    // Only count problems that will actually be included in the sheet (rated problems)
    const ratedProblems = sortedSubmissions.filter(
      (submission) => submission.problem.rating && submission.problem.rating > 0
    );

    ratedProblems.forEach((submission) => {
      const rating = submission.problem.rating!.toString();
      distribution[rating] = (distribution[rating] || 0) + 1;
    });

    // Sort ratings numerically for display
    return Object.entries(distribution)
      .sort(([a], [b]) => parseInt(a) - parseInt(b))
      .reduce((acc, [rating, count]) => {
        acc[rating] = count;
        return acc;
      }, {} as Record<string, number>);
  }, [sortedSubmissions]);

  // Get the problems to display based on current pagination
  // This prevents rendering thousands of problems at once for better performance
  const displayedSubmissions = React.useMemo(() => {
    return sortedSubmissions.slice(0, displayCount);
  }, [sortedSubmissions, displayCount]);

  // Function to load more problems (50 at a time)
  const handleShowMore = () => {
    setDisplayCount((prev) => prev + 50);
  };

  // Reset display count when submissions change (new user search)
  React.useEffect(() => {
    setDisplayCount(100);
  }, [submissions]);

  if (isLoading) {
    return (
      <div className="bg-gray-900 border border-gray-700 rounded-xl p-6 shadow-lg">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="bg-gray-800 rounded-lg p-4">
                <div className="h-5 bg-gray-700 rounded w-3/4 mb-2"></div>
                <div className="h-4 bg-gray-700 rounded w-1/2 mb-2"></div>
                <div className="flex gap-2">
                  <div className="h-6 bg-gray-700 rounded w-16"></div>
                  <div className="h-6 bg-gray-700 rounded w-20"></div>
                  <div className="h-6 bg-gray-700 rounded w-24"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-950 border border-red-800 rounded-xl p-6 shadow-lg">
        <div className="flex items-center gap-3 mb-3">
          <div className="w-4 h-4 bg-red-400 rounded-full"></div>
          <h3 className="text-xl font-semibold text-red-200">
            Error Loading Problems
          </h3>
        </div>
        <p className="text-red-300">{error}</p>
      </div>
    );
  }

  if (submissions.length === 0) {
    return (
      <div className="bg-gray-900 border border-gray-700 rounded-xl p-6 shadow-lg text-center">
        <div className="flex items-center justify-center gap-3 mb-3">
          <div className="w-4 h-4 bg-gray-400 rounded-full"></div>
          <h3 className="text-xl font-semibold text-gray-200">
            No Problems Found
          </h3>
        </div>
        <p className="text-gray-400">
          {dateRange
            ? `No problems solved between ${dateRange.startDate} and ${dateRange.endDate}`
            : "No problems match the current filters"}
        </p>
      </div>
    );
  }

  return (
    <div className="bg-gradient-to-br from-gray-900 to-gray-800 border border-gray-700 rounded-xl p-6 shadow-lg">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="w-4 h-4 bg-green-400 rounded-full"></div>
          <h3 className="text-xl font-semibold text-gray-200">
            Solved Problems
          </h3>
          <span className="text-sm text-gray-400">
            ({sortedSubmissions.length} problem
            {sortedSubmissions.length !== 1 ? "s" : ""})
          </span>
          <span className="text-xs text-gray-500">
            • unrated problems excluded && private contest problems don't count
          </span>
        </div>
        {dateRange && (
          <div className="text-sm text-gray-400">
            All problems solved • Graph range: {dateRange.startDate} to{" "}
            {dateRange.endDate}
          </div>
        )}
      </div>

      {/* Sheet Creation Promotion - Compact */}
      {submissions.length > 0 &&
        user &&
        userInfo &&
        userInfo.handle &&
        !sheetLoading &&
        sheetDetails &&
        sheetDetails.isAllowedToCreateMore &&
        sortedSubmissions.length <= 400 && (
          <div className="mb-4">
            <div className="bg-blue-900/30 border border-blue-500/30 rounded-lg p-4 backdrop-blur-sm">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                    <FileText className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <h4 className="text-sm font-semibold text-white">
                      🎯 Create Your Personalized Sheet
                    </h4>
                    <p className="text-blue-200 text-xs">
                      {sortedSubmissions.length} problems ready to organize
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <button
                    onClick={handleCreateSheet}
                    disabled={
                      sheetDetails && !sheetDetails.isAllowedToCreateMore
                    }
                    className={`px-3 py-1.5 text-xs font-medium rounded flex items-center gap-1 transition-colors ${
                      sheetDetails && !sheetDetails.isAllowedToCreateMore
                        ? "bg-gray-600 text-gray-400 cursor-not-allowed"
                        : "bg-blue-600 text-white hover:bg-blue-700"
                    }`}
                    title={
                      sheetDetails && !sheetDetails.isAllowedToCreateMore
                        ? `Sheet limit reached (${sheetDetails.count}/${sheetDetails.maxSheetSlots})`
                        : "Create a personalized coding sheet with these problems"
                    }
                  >
                    <FileText className="w-3 h-3" />
                    Create Sheet
                  </button>
                  <span className="text-xs text-blue-300">
                    {sheetDetails
                      ? `${sheetDetails.count}/${sheetDetails.maxSheetSlots} used`
                      : "Free"}
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

      {/* Sheet Limit Reached - Compact Warning */}
      {submissions.length > 0 &&
        user &&
        userInfo &&
        userInfo.handle &&
        !sheetLoading &&
        sheetDetails &&
        !sheetDetails.isAllowedToCreateMore && (
          <div className="mb-4">
            <div className="bg-red-900/30 border border-red-500/40 rounded-lg p-4 backdrop-blur-sm">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-8 h-8 bg-red-600 rounded-lg flex items-center justify-center">
                    <FileText className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <h4 className="text-sm font-semibold text-white flex items-center gap-2">
                      🚫 Sheet Limit Reached
                    </h4>
                    <p className="text-red-200 text-xs">
                      {sheetDetails.count}/{sheetDetails.maxSheetSlots} sheets
                      used
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Link
                    href="/customsheet"
                    className="px-3 py-1.5 bg-red-600 text-white text-xs font-medium rounded hover:bg-red-700 transition-colors flex items-center gap-1"
                  >
                    <FileText className="w-3 h-3" />
                    View Sheets
                  </Link>
                  <button
                    onClick={() => setIsDeleteModalOpen(true)}
                    disabled={
                      deletingSheetId !== null ||
                      sheetDetailsLoading ||
                      !sheetDetails?.sheets?.length
                    }
                    className="px-3 py-1.5 bg-gray-600 text-white text-xs font-medium rounded hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-1"
                    title={
                      sheetDetails?.sheets && sheetDetails.sheets.length > 1
                        ? `Manage ${sheetDetails.sheets.length} sheets`
                        : "Delete your existing coding sheet"
                    }
                  >
                    <Trash2 className="w-3 h-3" />
                    {sheetDetails?.sheets && sheetDetails.sheets.length > 1
                      ? `Manage`
                      : "Delete"}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

      {/* Loading State for Sheet Data */}
      {submissions.length > 0 &&
        user &&
        userInfo &&
        userInfo.handle &&
        sheetLoading && (
          <div className="mb-6 relative">
            <div className="absolute inset-0 bg-gradient-to-br from-gray-900/30 via-gray-800/20 to-gray-900/30 rounded-xl blur-lg"></div>

            <div className="relative bg-gradient-to-br from-gray-900/40 via-gray-800/30 to-gray-900/40 backdrop-blur-sm border border-gray-600/30 rounded-xl p-6 shadow-xl">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-gradient-to-br from-gray-600 to-gray-700 rounded-xl flex items-center justify-center shadow-lg animate-pulse">
                  <FileText className="w-6 h-6 text-white" />
                </div>
                <div className="flex-1">
                  <h4 className="text-lg font-semibold text-white mb-2">
                    ⏳ Checking Your Sheet Status...
                  </h4>
                  <p className="text-gray-300 text-sm">
                    Loading your sheet information to determine if you can
                    create a new sheet.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

      {/* Problems List */}
      <div className="space-y-4 max-h-96 scrollable-with-bar">
        {displayedSubmissions.map((submission, index) => {
          const { problem } = submission;
          const problemUrl = getProblemUrl(submission);
          const ratingColor = getRatingColor(problem.rating);

          return (
            <div
              key={`${problem.contestId}-${problem.index}-${index}`}
              className="bg-gray-800 border border-gray-600 rounded-lg p-4 hover:bg-gray-750 transition-colors"
            >
              <div className="flex items-start justify-between mb-3">
                <div className="flex-1">
                  <a
                    href={problemUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-lg font-semibold text-blue-400 hover:text-blue-300 transition-colors"
                  >
                    {problem.contestId
                      ? `${problem.contestId}${problem.index}. ${problem.name}`
                      : `${problem.index}. ${problem.name}`}
                  </a>
                  <div className="flex items-center gap-4 mt-1 text-sm text-gray-400">
                    <span>
                      Solved: {formatDate(submission.creationTimeSeconds)}
                    </span>
                    {problem.contestId && (
                      <span>Contest: {problem.contestId}</span>
                    )}
                    {submission.programmingLanguage && (
                      <span>Language: {submission.programmingLanguage}</span>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {problem.rating && (
                    <span
                      className="px-2 py-1 rounded text-sm font-medium"
                      style={{
                        backgroundColor: `${ratingColor}20`,
                        color: ratingColor,
                        border: `1px solid ${ratingColor}40`,
                      }}
                    >
                      {problem.rating}
                    </span>
                  )}
                  <a
                    href={problemUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="px-3 py-1 bg-blue-600 text-white text-sm rounded hover:bg-blue-700 transition-all duration-200 cursor-pointer hover:scale-105 active:scale-95"
                  >
                    View
                  </a>
                </div>
              </div>

              {/* Problem Tags */}
              {problem.tags.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {/* Deduplicate tags to prevent React key conflicts */}
                  {[...new Set(problem.tags)].map((tag, tagIndex) => (
                    <span
                      key={`${tag}-${tagIndex}`}
                      className="px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded-full"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Pagination Controls */}
      {sortedSubmissions.length > displayCount && (
        <div className="mt-4 text-center">
          <button
            onClick={handleShowMore}
            className="px-6 py-2 bg-gradient-to-r from-gray-700 to-gray-600 text-white font-medium rounded-lg hover:from-gray-600 hover:to-gray-500 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl hover:cursor-pointer"
          >
            Show More ({Math.min(50, sortedSubmissions.length - displayCount)}{" "}
            more)
          </button>
          <p className="text-sm text-gray-400 mt-2">
            Showing {displayedSubmissions.length} of {sortedSubmissions.length}{" "}
            problems
          </p>
        </div>
      )}

      {/* Show completion message when all problems are displayed */}
      {sortedSubmissions.length > 100 &&
        displayCount >= sortedSubmissions.length && (
          <div className="mt-4 text-center">
            <p className="text-sm text-gray-400">
              All {sortedSubmissions.length} problems displayed
            </p>
          </div>
        )}

      {/* Enhanced Create Coding Sheet Section */}
      {submissions.length > 0 && (
        <div className="mt-6 pt-6 border-t border-gray-600">
          <div className="flex items-center justify-between">
            <CodingSheetInfo problemCount={sortedSubmissions.length} />
            {/* Show create sheet button only if user has less than 1 sheet */}
            {!user ? (
              <button
                onClick={handleTabLogin}
                disabled={isAuthenticating}
                className="px-6 py-3 bg-gradient-to-r from-yellow-600 to-amber-600 text-white rounded-lg shadow-lg hover:from-yellow-700 hover:to-amber-700 transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
              >
                <div className="flex items-center gap-2">
                  {isAuthenticating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span className="font-medium">Signing in...</span>
                    </>
                  ) : (
                    <div className="flex flex-col items-center gap-1">
                      <div className="flex items-center gap-2">
                        <svg
                          className="w-4 h-4"
                          fill="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" />
                          <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" />
                          <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" />
                          <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" />
                        </svg>
                        <div className="font-medium">Login with Google</div>
                      </div>
                      <div className="text-xs opacity-90">
                        Step 1: Sign in to continue
                      </div>
                    </div>
                  )}
                </div>
              </button>
            ) : sheetLoading || isUserInfoLoading ? (
              <div className="px-6 py-3 bg-gray-600 text-gray-400 rounded-lg animate-pulse">
                <div className="font-medium">Loading...</div>
                <div className="text-xs">Checking sheet status</div>
              </div>
            ) : sheetDetails && !sheetDetails.isAllowedToCreateMore ? (
              <div className="px-6 py-3 bg-gradient-to-r from-red-600 to-red-700 text-white rounded-lg shadow-lg">
                <div className="font-medium">
                  Sheet limit reached ({sheetDetails.count}/
                  {sheetDetails.maxSheetSlots})
                </div>
                <div className="text-xs opacity-90">Maximum sheets created</div>
              </div>
            ) : userInfo && userInfo.handle ? (
              <button
                onClick={handleCreateSheet}
                disabled={
                  sortedSubmissions.length > 400 ||
                  (sheetDetails && !sheetDetails.isAllowedToCreateMore)
                }
                className={`px-8 py-3 font-semibold rounded-lg transition-all duration-300 shadow-lg ${
                  sortedSubmissions.length > 400 ||
                  (sheetDetails && !sheetDetails.isAllowedToCreateMore)
                    ? "bg-gray-600 text-gray-400 cursor-not-allowed"
                    : "bg-gradient-to-r from-blue-600 to-cyan-600 text-white hover:from-blue-700 hover:to-cyan-700 transform hover:scale-105 hover:shadow-xl"
                }`}
                title={
                  sheetDetails && !sheetDetails.isAllowedToCreateMore
                    ? `Sheet limit reached (${sheetDetails.count}/${sheetDetails.maxSheetSlots})`
                    : sortedSubmissions.length > 400
                    ? "Too many problems. Please use filters to reduce the count to 400 or fewer."
                    : "Create a coding sheet with these problems"
                }
              >
                <div className="flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  <div>
                    <div className="text-base">Create Sheet</div>
                    <div className="text-xs opacity-90">
                      {sortedSubmissions.length} problems
                    </div>
                  </div>
                </div>
              </button>
            ) : (
              <div className="flex flex-col items-end">
                <button
                  onClick={() => setIsVerifyModalOpen(true)}
                  className="px-4 py-2 bg-green-600 text-white text-sm font-medium rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2"
                  title="Connect your Codeforces handle to create sheets"
                >
                  <svg
                    className="w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M13 10V3L4 14h7v7l9-11h-7z"
                    />
                  </svg>
                  Connect Handle
                </button>
                <p className="text-xs text-gray-500 mt-2 text-right">
                  To prevent abuse, please connect to your codeforces account
                </p>
              </div>
            )}
          </div>
        </div>
      )}

      <TwoPartModal
        isOpen={isVerifyModalOpen}
        onClose={() => setIsVerifyModalOpen(false)}
        title="CodeForces Verification"
        onComplete={handleVerificationComplete}
        onVerificationSuccess={handleVerificationComplete}
      />

      {/* Create Sheet Confirmation Modal */}
      <Dialog
        open={isCreateSheetModalOpen}
        onOpenChange={setIsCreateSheetModalOpen}
      >
        <DialogContent className="w-[95vw] max-w-md sm:max-w-2xl lg:max-w-4xl max-h-[90vh] overflow-y-auto p-4 sm:p-6">
          <DialogHeader className="text-center space-y-3">
            <DialogTitle className="flex items-center justify-center gap-3 text-xl">
              <FileText className="h-6 w-6 text-blue-500" />
              Create Coding Sheet
            </DialogTitle>
            <DialogDescription className="text-base">
              {sortedSubmissions.length > 400 ? (
                <div className="space-y-2">
                  <div className="text-red-400 font-semibold">
                    ⚠️ Too many problems ({sortedSubmissions.length})
                  </div>
                  <div>
                    Cannot create sheet with more than 400 problems. Please use
                    filters to reduce the number of problems before creating a
                    sheet.
                  </div>
                </div>
              ) : (
                <>
                  Are you sure you want to create a coding sheet with{" "}
                  <span className="font-semibold text-blue-400">
                    {sortedSubmissions.length} problems
                  </span>
                  ?
                </>
              )}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {/* Sheet Name Input */}
            {sortedSubmissions.length <= 400 && (
              <div className="space-y-2">
                <label htmlFor="sheetName" className="text-sm font-medium">
                  Sheet Name <span className="text-red-500">*</span>
                </label>
                <input
                  id="sheetName"
                  type="text"
                  value={sheetName}
                  onChange={(e) => setSheetName(e.target.value)}
                  placeholder="Enter a name for your coding sheet"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                  maxLength={100}
                  suppressHydrationWarning={true}
                />
              </div>
            )}

            {/* Rating Distribution Section */}
            {Object.keys(ratingDistribution).length > 0 && (
              <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4 space-y-3">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-blue-400">
                    📊 Problems by Rating:
                  </span>
                </div>
                <div className="grid grid-cols-2 sm:grid-cols-3 gap-2 text-xs">
                  {Object.entries(ratingDistribution).map(([rating, count]) => (
                    <div
                      key={rating}
                      className="flex justify-between items-center bg-background/50 rounded px-2 py-1"
                    >
                      <span className="text-muted-foreground">{rating}:</span>
                      <span className="font-medium text-blue-400">{count}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {sortedSubmissions.length > 400 && (
              <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-4">
                <div className="flex items-center gap-2 text-red-600 dark:text-red-400">
                  <FileText className="h-4 w-4" />
                  <span className="text-sm font-medium">
                    Cannot Create Sheet
                  </span>
                </div>
                <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                  Please use filters (tags, rating range, date range, etc.) to
                  reduce the number of problems to 400 or fewer before creating
                  a sheet.
                </p>
              </div>
            )}
          </div>

          <DialogFooter className="gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => {
                setIsCreateSheetModalOpen(false);
                setSheetName(""); // Reset sheet name when closing
              }}
              disabled={isCreatingSheet}
              className="transition-all duration-200 hover:scale-105 active:scale-95"
            >
              {sortedSubmissions.length > 400 ? "Close" : "Cancel"}
            </Button>
            {sortedSubmissions.length <= 400 && (
              <Button
                type="button"
                onClick={handleConfirmCreateSheet}
                disabled={isCreatingSheet || !sheetName.trim()}
                className="flex items-center justify-center bg-emerald-600 hover:bg-emerald-700 text-white transition-all duration-200 hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isCreatingSheet ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    <span>Creating...</span>
                  </>
                ) : (
                  <span>Create Sheet</span>
                )}
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Sheet Selection Modal */}
      <Dialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <DialogContent className="w-[95vw] max-w-md sm:max-w-2xl lg:max-w-4xl max-h-[90vh] overflow-y-auto p-4 sm:p-6">
          <DialogHeader className="text-center space-y-3">
            <DialogTitle className="flex items-center justify-center gap-3 text-xl">
              <Trash2 className="h-6 w-6 text-red-500" />
              Manage Your Coding Sheets
            </DialogTitle>
            <DialogDescription className="text-base">
              You have {sheetDetails?.count || 0} active coding sheet
              {sheetDetails?.count !== 1 ? "s" : ""}. Select a sheet below to
              delete it permanently.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {sheetDetails?.sheets?.map((sheet: any, index: number) => {
              // Parse problems to get count and rating distribution
              let problemCount = 0;
              let ratingRange = "Unknown";

              try {
                const problems =
                  typeof sheet.problems === "string"
                    ? JSON.parse(sheet.problems)
                    : sheet.problems;

                if (Array.isArray(problems)) {
                  problemCount = problems.length;

                  const ratings = problems
                    .map((p) => parseInt(p.problemRating))
                    .filter((r) => !isNaN(r))
                    .sort((a, b) => a - b);

                  if (ratings.length > 0) {
                    ratingRange =
                      ratings.length === 1
                        ? `${ratings[0]}`
                        : `${ratings[0]} - ${ratings[ratings.length - 1]}`;
                  }
                }
              } catch (error) {
                // Handle parsing errors gracefully
                console.error("Error parsing sheet problems:", error);
              }

              return (
                <div
                  key={sheet.id}
                  className="border border-gray-600 rounded-lg p-4 hover:border-red-500/50 transition-colors bg-gray-800/50"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-white mb-2">
                        {sheet.name || `Sheet ${index + 1}`}
                      </h3>
                      <div className="space-y-1 text-sm text-gray-300">
                        <div className="flex items-center gap-4 text-xs text-gray-400">
                          <div className="flex items-center gap-1">
                            <FileText className="w-3 h-3" />
                            <span>{problemCount} problems</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="w-3 h-3" />
                            <span>
                              {new Date(sheet.createdAt).toLocaleDateString()}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col gap-2 ml-4">
                      <button
                        onClick={() => handleDeleteSheet(sheet.id)}
                        disabled={deletingSheetId === sheet.id}
                        className="px-4 py-2 bg-gradient-to-r from-red-600 to-red-700 text-white font-medium rounded-lg hover:from-red-700 hover:to-red-800 transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none flex items-center gap-2"
                      >
                        {deletingSheetId === sheet.id ? (
                          <>
                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            Deleting...
                          </>
                        ) : (
                          <>
                            <Trash2 className="w-4 h-4" />
                            Delete
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}

            {(!sheetDetails?.sheets || sheetDetails.sheets.length === 0) && (
              <div className="text-center py-8 text-gray-400">
                <Trash2 className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No sheets found to manage.</p>
              </div>
            )}
          </div>

          <DialogFooter className="gap-3">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteModalOpen(false)}
              disabled={deletingSheetId !== null}
              className="transition-all duration-200 hover:scale-105 active:scale-95"
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Alert Modal */}
      <Dialog
        open={alertModal.isOpen}
        onOpenChange={(open) => !open && closeAlert()}
      >
        <DialogContent className="max-w-md">
          <DialogHeader className="text-center space-y-3">
            <div className="flex justify-center">
              {alertModal.type === "success" && (
                <CheckCircle className="h-6 w-6 text-green-500" />
              )}
              {alertModal.type === "error" && (
                <XCircle className="h-6 w-6 text-red-500" />
              )}
              {alertModal.type === "warning" && (
                <AlertTriangle className="h-6 w-6 text-yellow-500" />
              )}
              {alertModal.type === "confirm" && (
                <AlertTriangle className="h-6 w-6 text-orange-500" />
              )}
              {alertModal.type === "info" && (
                <Info className="h-6 w-6 text-blue-500" />
              )}
            </div>
            <DialogTitle className="text-lg font-semibold">
              {alertModal.title}
            </DialogTitle>
            <DialogDescription className="text-base">
              {alertModal.message}
            </DialogDescription>
          </DialogHeader>

          <div
            className={`p-4 rounded-lg ${
              alertModal.type === "success"
                ? "border-green-500/20 bg-green-500/10"
                : alertModal.type === "error"
                ? "border-red-500/20 bg-red-500/10"
                : alertModal.type === "warning"
                ? "border-yellow-500/20 bg-yellow-500/10"
                : alertModal.type === "confirm"
                ? "border-orange-500/20 bg-orange-500/10"
                : "border-blue-500/20 bg-blue-500/10"
            }`}
          >
            <p className="text-sm text-center">
              {alertModal.type === "confirm"
                ? "Please confirm your action below."
                : alertModal.type === "success"
                ? "Operation completed successfully."
                : alertModal.type === "error"
                ? "Please review the error and try again."
                : "Please review the information above."}
            </p>
          </div>

          <DialogFooter className="gap-3">
            {alertModal.type === "confirm" && alertModal.onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  alertModal.onCancel?.();
                  closeAlert();
                }}
                className="transition-all duration-200 hover:scale-105 active:scale-95"
              >
                {alertModal.cancelText}
              </Button>
            )}
            <Button
              type="button"
              onClick={() => {
                alertModal.onConfirm?.();
                closeAlert();
              }}
              className={`transition-all duration-200 hover:scale-105 active:scale-95 ${
                alertModal.type === "success"
                  ? "bg-green-600 hover:bg-green-700"
                  : alertModal.type === "error"
                  ? "bg-red-600 hover:bg-red-700"
                  : alertModal.type === "warning"
                  ? "bg-yellow-600 hover:bg-yellow-700"
                  : alertModal.type === "confirm"
                  ? "bg-orange-600 hover:bg-orange-700"
                  : "bg-blue-600 hover:bg-blue-700"
              }`}
            >
              {alertModal.confirmText}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};
