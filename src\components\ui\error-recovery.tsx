"use client";

import { useQueryClient } from "@tanstack/react-query";
import { RefreshCw } from "lucide-react";
import { Button } from "./button";

interface ErrorRecoveryProps {
  error?: Error | null;
  onRetry?: () => void;
  queryKey?: string[];
  className?: string;
}

/**
 * Error Recovery Component
 * Provides manual error recovery with cache clearing capabilities
 */
export function ErrorRecovery({ 
  error, 
  onRetry, 
  queryKey,
  className = "" 
}: ErrorRecoveryProps) {
  const queryClient = useQueryClient();

  const handleRetry = () => {
    // Clear specific query cache if provided
    if (queryKey) {
      queryClient.invalidateQueries({ queryKey });
      queryClient.resetQueries({ queryKey });
    } else {
      // Clear all queries as fallback
      queryClient.clear();
    }

    // Call custom retry function if provided
    if (onRetry) {
      onRetry();
    } else {
      // Default: reload the page
      window.location.reload();
    }
  };

  if (!error) return null;

  return (
    <div className={`bg-red-950 border border-red-800 rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex-1">
          <h3 className="text-red-300 font-semibold">
            Something went wrong
          </h3>
          <p className="text-red-400 text-sm mt-1">
            {error.message || "An unexpected error occurred"}
          </p>
          <p className="text-red-500 text-xs mt-2">
            Try refreshing or clearing the cache to resolve this issue.
          </p>
        </div>
        <Button
          onClick={handleRetry}
          variant="outline"
          size="sm"
          className="ml-4 border-red-600 text-red-300 hover:bg-red-900 hover:text-red-200"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Retry
        </Button>
      </div>
    </div>
  );
}

/**
 * Hook for manual cache clearing
 * Useful for debugging and error recovery
 */
export function useCacheClear() {
  const queryClient = useQueryClient();

  const clearAllCache = () => {
    queryClient.clear();
    console.log("All React Query cache cleared");
  };

  const clearQueryCache = (queryKey: string[]) => {
    queryClient.invalidateQueries({ queryKey });
    queryClient.resetQueries({ queryKey });
    console.log(`Cache cleared for query: ${queryKey.join(", ")}`);
  };

  const clearUserCache = () => {
    const userQueries = [
      ["userInfo"],
      ["userProfile"],
      ["sheetInfo"],
      ["sheetDetails"],
      ["customSheets"],
      ["verificationStatus"],
      ["isVerified"]
    ];
    
    userQueries.forEach(queryKey => {
      queryClient.invalidateQueries({ queryKey });
      queryClient.resetQueries({ queryKey });
    });
    
    console.log("User-related cache cleared");
  };

  return {
    clearAllCache,
    clearQueryCache,
    clearUserCache
  };
}
