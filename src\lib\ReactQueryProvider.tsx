"use client";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useState } from "react";

export default function ReactQueryProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // Moderate staleTime to balance performance and freshness
            staleTime: 5 * 60 * 1000, // 5 minutes - reduced from 15 minutes

            // Keep cache time reasonable
            gcTime: 30 * 60 * 1000, // 30 minutes - reduced from 1 hour

            // Allow refetch on window focus for critical data
            refetchOnWindowFocus: false,

            // Allow refetch on reconnect for network recovery
            refetchOnReconnect: true,

            // CRITICAL FIX: Allow refetch on mount to recover from error states
            refetchOnMount: "always", // Always refetch to prevent cached error states

            // Improved retry configuration
            retry: (failureCount, error: any) => {
              // Don't retry 4xx errors except 401/403 (auth issues that might resolve)
              if (
                error?.response?.status >= 400 &&
                error?.response?.status < 500
              ) {
                // Retry auth errors once in case of token refresh
                if (
                  error?.response?.status === 401 ||
                  error?.response?.status === 403
                ) {
                  return failureCount < 1;
                }
                return false;
              }
              // Retry server errors up to 2 times
              return failureCount < 2;
            },

            // Shorter retry delay for faster error recovery
            retryDelay: (attemptIndex) =>
              Math.min(1000 * 2 ** attemptIndex, 30000), // Start at 1s, max 30s

            // CRITICAL FIX: Don't cache error states as long
            staleTime: (query) => {
              // If the query has an error, consider it stale immediately
              if (query.state.error) {
                return 0;
              }
              // Otherwise use the default staleTime
              return 5 * 60 * 1000; // 5 minutes
            },
          },
        },
      })
  );
  return (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
}
