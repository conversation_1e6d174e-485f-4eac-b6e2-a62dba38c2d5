# React Query Cache Issues - Fixes Applied

## Problem Summary

The TraceStack application was experiencing hidden caching issues where pages would open with errors, but work correctly after clearing the cache. This was caused by several React Query configuration and usage issues.

## Root Causes Identified

### 1. **Aggressive Error Caching**
- **Issue**: Global React Query config had `refetchOnMount: false`
- **Impact**: Error states were cached and never retried automatically
- **Result**: Pages showed cached errors instead of attempting fresh requests

### 2. **Inconsistent Query Keys**
- **Issue**: Mismatched query keys between queries and invalidations
- **Examples**:
  - Query: `["sheetInfo"]` vs Invalidation: `["sheetInfo", user.email]`
  - Query: `["userInfo", email]` vs Invalidation: `["userInfo"]`
- **Impact**: Cache invalidation didn't work properly

### 3. **Authentication Timing Issues**
- **Issue**: Queries ran before authentication was established
- **Impact**: Failed requests got cached, preventing retry on subsequent visits

## Fixes Applied

### 1. **Updated Global React Query Configuration**

**File**: `src/lib/ReactQueryProvider.tsx`

**Key Changes**:
- ✅ `refetchOnMount: "always"` - Always refetch to recover from error states
- ✅ `staleTime: 5 minutes` (reduced from 15 minutes)
- ✅ `gcTime: 30 minutes` (reduced from 1 hour)
- ✅ Improved retry logic for auth errors (401/403)
- ✅ Dynamic staleTime: Error states are immediately stale
- ✅ Better retry delays and limits

### 2. **Standardized Query Keys**

**File**: `src/lib/query-keys.ts` (NEW)

**Features**:
- ✅ Centralized query key management
- ✅ Consistent patterns across all queries
- ✅ Helper functions for bulk invalidation
- ✅ Type-safe query key generation

### 3. **Fixed Query Key Inconsistencies**

**File**: `src/components/codeforces/ProblemsDisplay.tsx`

**Changes**:
- ✅ Consistent `["sheetInfo"]` pattern
- ✅ Proper invalidation after login/logout
- ✅ Improved error handling with retry logic
- ✅ Better authentication state management

### 4. **Added Development Tools**

**Files**: 
- `src/components/dev/cache-debugger.tsx` (NEW)
- `src/components/ui/error-recovery.tsx` (NEW)

**Features**:
- ✅ Visual cache inspector (dev mode only)
- ✅ Manual cache clearing utilities
- ✅ Error recovery components
- ✅ Query status monitoring

## Testing Recommendations

### 1. **Error Recovery Testing**
```bash
# Test scenarios:
1. Open page → Force network error → Refresh page
2. Login → Logout → Login again
3. Create sheet → Delete sheet → Create again
4. Switch between authenticated/unauthenticated states
```

### 2. **Cache Debugging (Development)**
- Look for "Cache Debug" button in bottom-right corner
- Monitor query states and error conditions
- Use manual cache clearing for testing

### 3. **Authentication Flow Testing**
```bash
# Test sequence:
1. Open app (unauthenticated)
2. Try to create sheet → Should prompt login
3. Login → Should invalidate all user queries
4. Create sheet → Should work without errors
5. Refresh page → Should maintain state
```

## Monitoring & Maintenance

### 1. **Query Key Consistency**
- Always use `src/lib/query-keys.ts` for new queries
- Update helper functions when adding new query types
- Ensure invalidation uses same keys as queries

### 2. **Error State Monitoring**
- Watch for persistent error states in production
- Monitor authentication-related query failures
- Use cache debugger in development for troubleshooting

### 3. **Performance Considerations**
- `refetchOnMount: "always"` may increase API calls
- Monitor for unnecessary refetches
- Adjust staleTime per query type if needed

## Expected Improvements

1. **Reduced Cache-Related Errors**: Pages should load correctly on first visit
2. **Better Error Recovery**: Automatic retry on authentication issues
3. **Consistent Behavior**: Predictable cache invalidation after user actions
4. **Improved Development Experience**: Visual cache debugging tools
5. **Faster Error Resolution**: Immediate retry for failed queries

## Future Enhancements

1. **Query-Specific Configurations**: Fine-tune cache settings per query type
2. **Background Sync**: Implement background data synchronization
3. **Offline Support**: Add offline-first caching strategies
4. **Performance Monitoring**: Add query performance metrics
5. **Smart Invalidation**: Context-aware cache invalidation patterns
