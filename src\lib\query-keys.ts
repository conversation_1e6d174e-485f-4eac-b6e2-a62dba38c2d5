/**
 * Centralized query key management for React Query
 * This ensures consistency across all queries and invalidations
 */

export const queryKeys = {
  // User-related queries
  userInfo: (email?: string) => email ? ["userInfo", email] : ["userInfo"],
  userProfile: (email?: string) => email ? ["userProfile", email] : ["userProfile"],
  
  // Sheet-related queries
  sheetInfo: (email?: string) => email ? ["sheetInfo", email] : ["sheetInfo"],
  sheetDetails: (email?: string) => email ? ["sheetDetails", email] : ["sheetDetails"],
  customSheets: () => ["customSheets"],
  
  // Codeforces-related queries
  codeforcesSubmissions: (handle: string) => ["codeforces-submissions-base", handle],
  codeforcesRating: (handle: string) => ["codeforces-rating-client", handle],
  codeforcesUserInfo: (handle: string) => ["codeforces-user-info-client", handle],
  codeforcesSolved: (handle: string) => ["codeforces-all-solved", handle],
  
  // Verification queries
  verificationStatus: () => ["verificationStatus"],
  isVerified: () => ["isVerified"],
} as const;

/**
 * Helper function to invalidate all queries for a specific user
 * Useful after login/logout or major user state changes
 */
export const invalidateUserQueries = (queryClient: any, email?: string) => {
  queryClient.invalidateQueries({ queryKey: ["userInfo"] });
  queryClient.invalidateQueries({ queryKey: ["userProfile"] });
  queryClient.invalidateQueries({ queryKey: ["sheetInfo"] });
  queryClient.invalidateQueries({ queryKey: ["sheetDetails"] });
  queryClient.invalidateQueries({ queryKey: ["customSheets"] });
  queryClient.invalidateQueries({ queryKey: ["verificationStatus"] });
  queryClient.invalidateQueries({ queryKey: ["isVerified"] });
};

/**
 * Helper function to invalidate sheet-related queries
 * Useful after creating, updating, or deleting sheets
 */
export const invalidateSheetQueries = (queryClient: any, email?: string) => {
  queryClient.invalidateQueries({ queryKey: ["sheetInfo"] });
  queryClient.invalidateQueries({ queryKey: ["sheetDetails"] });
  queryClient.invalidateQueries({ queryKey: ["customSheets"] });
};

/**
 * Helper function to clear error states for specific queries
 * Useful for manual error recovery
 */
export const clearQueryErrors = (queryClient: any, queryKey: string[]) => {
  queryClient.resetQueries({ queryKey, exact: false });
};
