"use client";

import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Trash2, RefreshCw, Eye, EyeOff } from "lucide-react";

/**
 * Cache Debugger Component
 * Only shows in development mode
 * Helps debug React Query cache issues
 */
export function CacheDebugger() {
  const queryClient = useQueryClient();
  const [isVisible, setIsVisible] = useState(false);
  const [selectedQuery, setSelectedQuery] = useState<string | null>(null);

  // Only show in development
  if (process.env.NODE_ENV !== "development") {
    return null;
  }

  const queryCache = queryClient.getQueryCache();
  const queries = queryCache.getAll();

  const clearAllCache = () => {
    queryClient.clear();
    console.log("🧹 All cache cleared");
  };

  const clearQuery = (queryKey: string[]) => {
    queryClient.invalidateQueries({ queryKey });
    queryClient.resetQueries({ queryKey });
    console.log(`🧹 Cache cleared for: ${queryKey.join(", ")}`);
  };

  const refetchQuery = (queryKey: string[]) => {
    queryClient.refetchQueries({ queryKey });
    console.log(`🔄 Refetching: ${queryKey.join(", ")}`);
  };

  const getQueryStatus = (query: any) => {
    if (query.state.error) return "error";
    if (query.state.isLoading) return "loading";
    if (query.state.data) return "success";
    return "idle";
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "error": return "bg-red-500";
      case "loading": return "bg-yellow-500";
      case "success": return "bg-green-500";
      default: return "bg-gray-500";
    }
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsVisible(true)}
          variant="outline"
          size="sm"
          className="bg-black/80 border-gray-600 text-white hover:bg-gray-800"
        >
          <Eye className="w-4 h-4 mr-2" />
          Cache Debug
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96 max-h-96 overflow-hidden">
      <Card className="bg-black/90 border-gray-600 text-white">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm">React Query Cache</CardTitle>
            <div className="flex gap-2">
              <Button
                onClick={clearAllCache}
                variant="outline"
                size="sm"
                className="h-6 px-2 text-xs border-red-600 text-red-300 hover:bg-red-900"
              >
                <Trash2 className="w-3 h-3 mr-1" />
                Clear All
              </Button>
              <Button
                onClick={() => setIsVisible(false)}
                variant="outline"
                size="sm"
                className="h-6 px-2 text-xs border-gray-600"
              >
                <EyeOff className="w-3 h-3" />
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent className="pt-0 max-h-80 overflow-y-auto">
          <div className="space-y-2">
            {queries.map((query, index) => {
              const queryKey = query.queryKey as string[];
              const status = getQueryStatus(query);
              const keyString = queryKey.join(" → ");
              
              return (
                <div
                  key={index}
                  className="p-2 border border-gray-700 rounded text-xs"
                >
                  <div className="flex items-center justify-between mb-1">
                    <div className="flex items-center gap-2">
                      <Badge 
                        className={`${getStatusColor(status)} text-white text-xs px-1 py-0`}
                      >
                        {status}
                      </Badge>
                      <span className="font-mono text-xs truncate">
                        {keyString}
                      </span>
                    </div>
                    <div className="flex gap-1">
                      <Button
                        onClick={() => refetchQuery(queryKey)}
                        variant="outline"
                        size="sm"
                        className="h-5 px-1 text-xs border-blue-600 text-blue-300 hover:bg-blue-900"
                      >
                        <RefreshCw className="w-3 h-3" />
                      </Button>
                      <Button
                        onClick={() => clearQuery(queryKey)}
                        variant="outline"
                        size="sm"
                        className="h-5 px-1 text-xs border-red-600 text-red-300 hover:bg-red-900"
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </div>
                  
                  {query.state.error && (
                    <div className="text-red-400 text-xs mt-1 p-1 bg-red-950/50 rounded">
                      Error: {(query.state.error as Error).message}
                    </div>
                  )}
                  
                  <div className="text-gray-400 text-xs mt-1">
                    Updated: {new Date(query.state.dataUpdatedAt).toLocaleTimeString()}
                  </div>
                </div>
              );
            })}
            
            {queries.length === 0 && (
              <div className="text-gray-400 text-center py-4">
                No cached queries
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
